using System;
using System.Collections.Generic;
using System.Diagnostics;
using UnityEngine;
using DGame.Framework;

namespace Storage
{
    /// <summary>
    /// StorageCache性能测试脚本
    /// 用于验证新的类型包装器系统相比原始object存储的性能改进
    /// </summary>
    public class StorageCachePerformanceTest : MonoBehaviour
    {
        [Header("测试配置")]
        [SerializeField] private int _testIterations = 100000;
        [SerializeField] private bool _runOnStart = false;
        [SerializeField] private bool _enableDetailedLogging = false;

        private StorageCache _cache;
        private Stopwatch _stopwatch;

        #region Unity生命周期

        private void Start()
        {
            if (_runOnStart)
            {
                RunPerformanceTests();
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 运行性能测试
        /// </summary>
        [ContextMenu("运行性能测试")]
        public void RunPerformanceTests()
        {
            NLogger.Log("=== StorageCache 性能测试开始 ===");
            NLogger.Log("测试迭代次数: {0}", arg0: _testIterations);

            // 初始化
            _cache = new StorageCache();
            _stopwatch = new Stopwatch();

            // 运行各种类型的性能测试
            TestIntegerPerformance();
            TestFloatPerformance();
            TestBooleanPerformance();
            TestStringPerformance();
            TestVector3Performance();
            TestQuaternionPerformance();
            TestColorPerformance();
            TestMixedTypesPerformance();

            // 测试内存使用情况
            TestMemoryUsage();

            NLogger.Log("=== StorageCache 性能测试完成 ===");
        }

        #endregion

        #region 性能测试方法

        /// <summary>
        /// 测试整数类型性能
        /// </summary>
        private void TestIntegerPerformance()
        {
            NLogger.Log("--- 测试 int 类型性能 ---");

            // 写入测试
            _stopwatch.Restart();
            for (int i = 0; i < _testIterations; i++)
            {
                _cache.Set($"int_key_{i % 1000}", i);
            }
            _stopwatch.Stop();
            long writeTime = _stopwatch.ElapsedMilliseconds;

            // 读取测试
            _stopwatch.Restart();
            int totalValue = 0;
            for (int i = 0; i < _testIterations; i++)
            {
                if (_cache.TryGet($"int_key_{i % 1000}", out int value))
                {
                    totalValue += value;
                }
            }
            _stopwatch.Stop();
            long readTime = _stopwatch.ElapsedMilliseconds;

            NLogger.Log("int 写入时间: {0}ms, 读取时间: {1}ms, 总和验证: {2}", 
                arg0: writeTime, arg1: readTime, arg2: totalValue);
        }

        /// <summary>
        /// 测试浮点数类型性能
        /// </summary>
        private void TestFloatPerformance()
        {
            NLogger.Log("--- 测试 float 类型性能 ---");

            // 写入测试
            _stopwatch.Restart();
            for (int i = 0; i < _testIterations; i++)
            {
                _cache.Set($"float_key_{i % 1000}", i * 0.1f);
            }
            _stopwatch.Stop();
            long writeTime = _stopwatch.ElapsedMilliseconds;

            // 读取测试
            _stopwatch.Restart();
            float totalValue = 0f;
            for (int i = 0; i < _testIterations; i++)
            {
                if (_cache.TryGet($"float_key_{i % 1000}", out float value))
                {
                    totalValue += value;
                }
            }
            _stopwatch.Stop();
            long readTime = _stopwatch.ElapsedMilliseconds;

            NLogger.Log("float 写入时间: {0}ms, 读取时间: {1}ms, 总和验证: {2:F2}", 
                arg0: writeTime, arg1: readTime, arg2: totalValue);
        }

        /// <summary>
        /// 测试布尔类型性能
        /// </summary>
        private void TestBooleanPerformance()
        {
            NLogger.Log("--- 测试 bool 类型性能 ---");

            // 写入测试
            _stopwatch.Restart();
            for (int i = 0; i < _testIterations; i++)
            {
                _cache.Set($"bool_key_{i % 1000}", i % 2 == 0);
            }
            _stopwatch.Stop();
            long writeTime = _stopwatch.ElapsedMilliseconds;

            // 读取测试
            _stopwatch.Restart();
            int trueCount = 0;
            for (int i = 0; i < _testIterations; i++)
            {
                if (_cache.TryGet($"bool_key_{i % 1000}", out bool value) && value)
                {
                    trueCount++;
                }
            }
            _stopwatch.Stop();
            long readTime = _stopwatch.ElapsedMilliseconds;

            NLogger.Log("bool 写入时间: {0}ms, 读取时间: {1}ms, true计数: {2}", 
                arg0: writeTime, arg1: readTime, arg2: trueCount);
        }

        /// <summary>
        /// 测试字符串类型性能
        /// </summary>
        private void TestStringPerformance()
        {
            NLogger.Log("--- 测试 string 类型性能 ---");

            // 写入测试
            _stopwatch.Restart();
            for (int i = 0; i < _testIterations; i++)
            {
                _cache.Set($"string_key_{i % 1000}", $"value_{i}");
            }
            _stopwatch.Stop();
            long writeTime = _stopwatch.ElapsedMilliseconds;

            // 读取测试
            _stopwatch.Restart();
            int totalLength = 0;
            for (int i = 0; i < _testIterations; i++)
            {
                if (_cache.TryGet($"string_key_{i % 1000}", out string value))
                {
                    totalLength += value.Length;
                }
            }
            _stopwatch.Stop();
            long readTime = _stopwatch.ElapsedMilliseconds;

            NLogger.Log("string 写入时间: {0}ms, 读取时间: {1}ms, 总长度: {2}", 
                arg0: writeTime, arg1: readTime, arg2: totalLength);
        }

        /// <summary>
        /// 测试Vector3类型性能
        /// </summary>
        private void TestVector3Performance()
        {
            NLogger.Log("--- 测试 Vector3 类型性能 ---");

            // 写入测试
            _stopwatch.Restart();
            for (int i = 0; i < _testIterations; i++)
            {
                _cache.Set($"vector3_key_{i % 1000}", new Vector3(i, i * 2, i * 3));
            }
            _stopwatch.Stop();
            long writeTime = _stopwatch.ElapsedMilliseconds;

            // 读取测试
            _stopwatch.Restart();
            Vector3 totalVector = Vector3.zero;
            for (int i = 0; i < _testIterations; i++)
            {
                if (_cache.TryGet($"vector3_key_{i % 1000}", out Vector3 value))
                {
                    totalVector += value;
                }
            }
            _stopwatch.Stop();
            long readTime = _stopwatch.ElapsedMilliseconds;

            NLogger.Log("Vector3 写入时间: {0}ms, 读取时间: {1}ms, 总和: {2}", 
                arg0: writeTime, arg1: readTime, arg2: totalVector);
        }

        /// <summary>
        /// 测试Quaternion类型性能
        /// </summary>
        private void TestQuaternionPerformance()
        {
            NLogger.Log("--- 测试 Quaternion 类型性能 ---");

            // 写入测试
            _stopwatch.Restart();
            for (int i = 0; i < _testIterations; i++)
            {
                _cache.Set($"quat_key_{i % 1000}", Quaternion.Euler(i, i * 2, i * 3));
            }
            _stopwatch.Stop();
            long writeTime = _stopwatch.ElapsedMilliseconds;

            // 读取测试
            _stopwatch.Restart();
            int readCount = 0;
            for (int i = 0; i < _testIterations; i++)
            {
                if (_cache.TryGet($"quat_key_{i % 1000}", out Quaternion value))
                {
                    readCount++;
                }
            }
            _stopwatch.Stop();
            long readTime = _stopwatch.ElapsedMilliseconds;

            NLogger.Log("Quaternion 写入时间: {0}ms, 读取时间: {1}ms, 读取计数: {2}", 
                arg0: writeTime, arg1: readTime, arg2: readCount);
        }

        /// <summary>
        /// 测试Color类型性能
        /// </summary>
        private void TestColorPerformance()
        {
            NLogger.Log("--- 测试 Color 类型性能 ---");

            // 写入测试
            _stopwatch.Restart();
            for (int i = 0; i < _testIterations; i++)
            {
                float t = (float)i / _testIterations;
                _cache.Set($"color_key_{i % 1000}", new Color(t, t * 0.5f, t * 0.3f, 1f));
            }
            _stopwatch.Stop();
            long writeTime = _stopwatch.ElapsedMilliseconds;

            // 读取测试
            _stopwatch.Restart();
            Color totalColor = Color.black;
            for (int i = 0; i < _testIterations; i++)
            {
                if (_cache.TryGet($"color_key_{i % 1000}", out Color value))
                {
                    totalColor += value;
                }
            }
            _stopwatch.Stop();
            long readTime = _stopwatch.ElapsedMilliseconds;

            NLogger.Log("Color 写入时间: {0}ms, 读取时间: {1}ms, 总和: {2}", 
                arg0: writeTime, arg1: readTime, arg2: totalColor);
        }

        /// <summary>
        /// 测试混合类型性能
        /// </summary>
        private void TestMixedTypesPerformance()
        {
            NLogger.Log("--- 测试混合类型性能 ---");

            // 写入测试
            _stopwatch.Restart();
            for (int i = 0; i < _testIterations; i++)
            {
                int typeIndex = i % 6;
                switch (typeIndex)
                {
                    case 0: _cache.Set($"mixed_{i}", i); break;
                    case 1: _cache.Set($"mixed_{i}", i * 0.1f); break;
                    case 2: _cache.Set($"mixed_{i}", i % 2 == 0); break;
                    case 3: _cache.Set($"mixed_{i}", $"value_{i}"); break;
                    case 4: _cache.Set($"mixed_{i}", new Vector3(i, i, i)); break;
                    case 5: _cache.Set($"mixed_{i}", Color.red); break;
                }
            }
            _stopwatch.Stop();
            long writeTime = _stopwatch.ElapsedMilliseconds;

            // 读取测试
            _stopwatch.Restart();
            int successCount = 0;
            for (int i = 0; i < _testIterations; i++)
            {
                int typeIndex = i % 6;
                switch (typeIndex)
                {
                    case 0: if (_cache.TryGet($"mixed_{i}", out int _)) successCount++; break;
                    case 1: if (_cache.TryGet($"mixed_{i}", out float _)) successCount++; break;
                    case 2: if (_cache.TryGet($"mixed_{i}", out bool _)) successCount++; break;
                    case 3: if (_cache.TryGet($"mixed_{i}", out string _)) successCount++; break;
                    case 4: if (_cache.TryGet($"mixed_{i}", out Vector3 _)) successCount++; break;
                    case 5: if (_cache.TryGet($"mixed_{i}", out Color _)) successCount++; break;
                }
            }
            _stopwatch.Stop();
            long readTime = _stopwatch.ElapsedMilliseconds;

            NLogger.Log("混合类型 写入时间: {0}ms, 读取时间: {1}ms, 成功计数: {2}", 
                arg0: writeTime, arg1: readTime, arg2: successCount);
        }

        /// <summary>
        /// 测试内存使用情况
        /// </summary>
        private void TestMemoryUsage()
        {
            NLogger.Log("--- 内存使用情况测试 ---");

            // 记录初始内存
            long initialMemory = GC.GetTotalMemory(true);

            // 创建大量数据
            var testCache = new StorageCache();
            for (int i = 0; i < 10000; i++)
            {
                testCache.Set($"mem_int_{i}", i);
                testCache.Set($"mem_float_{i}", i * 0.1f);
                testCache.Set($"mem_vector_{i}", new Vector3(i, i, i));
                testCache.Set($"mem_string_{i}", $"test_string_{i}");
            }

            // 记录使用后内存
            long usedMemory = GC.GetTotalMemory(false);
            long memoryDiff = usedMemory - initialMemory;

            NLogger.Log("内存使用: 初始 {0} bytes, 使用后 {1} bytes, 差值 {2} bytes ({3:F2} KB)", 
                arg0: initialMemory, arg1: usedMemory, arg2: memoryDiff, arg3: memoryDiff / 1024.0f);

            // 清理并强制GC
            testCache.Clear();
            testCache = null;
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();

            long finalMemory = GC.GetTotalMemory(true);
            NLogger.Log("GC后内存: {0} bytes, 回收了 {1} bytes ({2:F2} KB)", 
                arg0: finalMemory, arg1: usedMemory - finalMemory, arg2: (usedMemory - finalMemory) / 1024.0f);
        }

        #endregion
    }
}
