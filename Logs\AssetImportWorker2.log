Using pre-set license
Built from '2022.3/china_unity/release' branch; Version is '2022.3.57f1c2 (32588f90613b) revision 3299471'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'zh' Physical Memory: 32686 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
E:\Program Files\Unity\Editor\2022.3.57f1c2\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
H:/DiceGame/DGame
-logFile
Logs/AssetImportWorker2.log
-srvPort
60295
Successfully changed project path to: H:/DiceGame/DGame
H:/DiceGame/DGame
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [32680]  Target information:

Player connection [32680]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 649026953 [EditorId] 649026953 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-31EMADL) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [32680]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 649026953 [EditorId] 649026953 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-31EMADL) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [32680]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 649026953 [EditorId] 649026953 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-31EMADL) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [32680] Host joined multi-casting on [***********:54997]...
Player connection [32680] Host joined alternative multi-casting on [***********:34997]...
Input System module state changed to: Initialized.
[PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
Refreshing native plugins compatible for Editor in 11.34 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.57f1c2 (32588f90613b)
[Subsystems] Discovering subsystems at path E:/Program Files/Unity/Editor/2022.3.57f1c2/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/DiceGame/DGame/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 3080 (ID=0x2206)
    Vendor:   NVIDIA
    VRAM:     10053 MB
    Driver:   32.0.15.7652
Initialize mono
Mono path[0] = 'E:/Program Files/Unity/Editor/2022.3.57f1c2/Editor/Data/Managed'
Mono path[1] = 'E:/Program Files/Unity/Editor/2022.3.57f1c2/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Program Files/Unity/Editor/2022.3.57f1c2/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56912
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Program Files/Unity/Editor/2022.3.57f1c2/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.002569 seconds.
- Loaded All Assemblies, in  0.224 seconds
Native extension for WindowsStandalone target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.198 seconds
Domain Reload Profiling: 422ms
	BeginReloadAssembly (70ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (88ms)
		LoadAssemblies (70ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (87ms)
			TypeCache.Refresh (86ms)
				TypeCache.ScanAssembly (78ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (198ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (165ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (6ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (117ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.573 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.634 seconds
Domain Reload Profiling: 1207ms
	BeginReloadAssembly (96ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (14ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (401ms)
		LoadAssemblies (278ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (178ms)
			TypeCache.Refresh (146ms)
				TypeCache.ScanAssembly (133ms)
			ScanForSourceGeneratedMonoScriptInfo (21ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (635ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (518ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (101ms)
			ProcessInitializeOnLoadAttributes (342ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.04 seconds
Refreshing native plugins compatible for Editor in 5.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6136 Unused Serialized files (Serialized files now loaded: 0)
Unloading 42 unused Assets / (350.1 KB). Loaded Objects now: 6731.
Memory consumption went from 235.4 MB to 235.1 MB.
Total: 3.296100 ms (FindLiveObjects: 0.314300 ms CreateObjectMapping: 0.244800 ms MarkObjects: 2.608200 ms  DeleteObjects: 0.128100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemTest.cs: 6e02a73794208c8111900b7754ef579a -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/StorageCacheV2.cs: 8022bfbbf077d75662294ee2ce0494c5 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageTypeWrapper.cs: 799147bd510e7b8f21afd6c854e50a95 -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/StorageType_string.cs: f1c0ee407fa20bb6a3f55743ae44bc01 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/StorageType_float.cs: 910016c00f4bc21d02b861fd3ad6cff4 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/StorageTypeMgr.cs: 1d60c4a2b4d6ac4f9ab0b11f9bbfef47 -> 
  custom:scripting/monoscript/fileName/StorageType_int.cs: c52d6ca8d70163e6abd6c217a3751919 -> 
  custom:scripting/monoscript/fileName/StorageType_bool.cs: 9818a5a72b020d32a15f7b96f088344e -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 51075.929540 seconds.
  path: Assets/Scripts/Framework/Storage/Core/StorageTypeWrapper.cs
  artifactKey: Guid(711c11762d6494b4aae40d47d07ed80c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Framework/Storage/Core/StorageTypeWrapper.cs using Guid(711c11762d6494b4aae40d47d07ed80c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
 -> (artifact id: '66b05f864cb30151594bcfaee91056f0') in 0.028463 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  1.089 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.674 seconds
Domain Reload Profiling: 1765ms
	BeginReloadAssembly (645ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (467ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (81ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (373ms)
		LoadAssemblies (394ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (42ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (674ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (524ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (105ms)
			ProcessInitializeOnLoadAttributes (346ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 7.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5968 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.6 KB). Loaded Objects now: 6738.
Memory consumption went from 206.0 MB to 205.7 MB.
Total: 4.292000 ms (FindLiveObjects: 0.386500 ms CreateObjectMapping: 0.295500 ms MarkObjects: 3.528700 ms  DeleteObjects: 0.080600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemTest.cs: 6e02a73794208c8111900b7754ef579a -> 
  custom:scripting/monoscript/fileName/StorageCachePerformanceTest.cs: 810a91afa3433152557aa6d0203dc5c7 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/StorageCacheV2.cs: 8022bfbbf077d75662294ee2ce0494c5 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:scripting/monoscript/fileName/StorageTypeConverter.cs: ced5c46ed1b2bcfa05b15566d0dd740e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageTypeWrapper.cs: 8f3fe27bd2c7f3fdf210ab110b81d87c -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/StorageType_string.cs: f1c0ee407fa20bb6a3f55743ae44bc01 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/StorageType_float.cs: 910016c00f4bc21d02b861fd3ad6cff4 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/StorageTypeMgr.cs: e109819f414051191819c259cf31e28b -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemExample.cs: dc415107117cba3a2df55398f422588b -> 
  custom:scripting/monoscript/fileName/StorageType_int.cs: c52d6ca8d70163e6abd6c217a3751919 -> 
  custom:scripting/monoscript/fileName/StorageType_bool.cs: 9818a5a72b020d32a15f7b96f088344e -> 
  custom:scripting/monoscript/fileName/StorageType_Primitives.cs: 9ae0bf72a9f391e65078ecd7c13cc164 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/StorageType_Unity.cs: b84105f2d8f5844ecdec8f7f9d0a0b05 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: fb9670a3f86a01551d45fde1f181c407 -> e3b0619a2e8f8221a937ec724b8b71bb
========================================================================
Received Import Request.
  Time since last request: 507.802706 seconds.
  path: Assets/Scripts/Framework/Storage/Core/StorageTypeConverter.cs
  artifactKey: Guid(5bf12c8dc73456d4f885349178dab2df) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Framework/Storage/Core/StorageTypeConverter.cs using Guid(5bf12c8dc73456d4f885349178dab2df) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
 -> (artifact id: 'e24663eb4c032f32f796b32060b1f29e') in 0.029873 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 0.704223 seconds.
  path: Assets/Scripts/Framework/Storage/Core/StorageTypeMgr.cs
  artifactKey: Guid(46f0cad1906dcd247a8fc722bce9b565) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Framework/Storage/Core/StorageTypeMgr.cs using Guid(46f0cad1906dcd247a8fc722bce9b565) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
 -> (artifact id: 'fee4b544af46331a2b101b1eebeb4803') in 0.000497 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 2.031878 seconds.
  path: Assets/Scripts/Framework/Storage/Core/StorageCache.cs
  artifactKey: Guid(4f8be3e7f0e7c5f429090a9be41dda92) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Framework/Storage/Core/StorageCache.cs using Guid(4f8be3e7f0e7c5f429090a9be41dda92) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
 -> (artifact id: '8db13f006cfbd63c57337e0ce4f5b014') in 0.000530 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 3.274587 seconds.
  path: Assets/Scripts/Framework/Storage/StorageTypeSystemExample.cs
  artifactKey: Guid(521929846f575654f831eab61cef7914) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Framework/Storage/StorageTypeSystemExample.cs using Guid(521929846f575654f831eab61cef7914) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
 -> (artifact id: '9984223e9d514048723e36846f33f883') in 0.000487 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 7.303645 seconds.
  path: Assets/Scripts/Framework/Storage/StorageCachePerformanceTest.cs
  artifactKey: Guid(21c3c54b0d856184892e457e1142d0fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Framework/Storage/StorageCachePerformanceTest.cs using Guid(21c3c54b0d856184892e457e1142d0fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 32 workers.
 -> (artifact id: 'ac7dff405331fc28320ba81a1438f679') in 0.000413 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.496 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.107 seconds
Domain Reload Profiling: 1602ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (273ms)
		LoadAssemblies (320ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1107ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (562ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (106ms)
			ProcessInitializeOnLoadAttributes (380ms)
			ProcessInitializeOnLoadMethodAttributes (66ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.93 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5968 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.7 KB). Loaded Objects now: 6744.
Memory consumption went from 206.0 MB to 205.7 MB.
Total: 5.147000 ms (FindLiveObjects: 0.678700 ms CreateObjectMapping: 0.362700 ms MarkObjects: 3.978400 ms  DeleteObjects: 0.125800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemTest.cs: 6e02a73794208c8111900b7754ef579a -> 
  custom:scripting/monoscript/fileName/StorageCachePerformanceTest.cs: 810a91afa3433152557aa6d0203dc5c7 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/StorageCacheV2.cs: 8022bfbbf077d75662294ee2ce0494c5 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:scripting/monoscript/fileName/StorageTypeConverter.cs: ced5c46ed1b2bcfa05b15566d0dd740e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageTypeWrapper.cs: 8f3fe27bd2c7f3fdf210ab110b81d87c -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/StorageType_string.cs: f1c0ee407fa20bb6a3f55743ae44bc01 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/StorageType_float.cs: 910016c00f4bc21d02b861fd3ad6cff4 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/StorageTypeMgr.cs: e109819f414051191819c259cf31e28b -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemExample.cs: dc415107117cba3a2df55398f422588b -> 
  custom:scripting/monoscript/fileName/StorageType_int.cs: c52d6ca8d70163e6abd6c217a3751919 -> 
  custom:scripting/monoscript/fileName/StorageType_bool.cs: 9818a5a72b020d32a15f7b96f088344e -> 
  custom:scripting/monoscript/fileName/StorageType_Primitives.cs: 9ae0bf72a9f391e65078ecd7c13cc164 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/StorageType_Unity.cs: b84105f2d8f5844ecdec8f7f9d0a0b05 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: fb9670a3f86a01551d45fde1f181c407 -> e3b0619a2e8f8221a937ec724b8b71bb
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.421 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.048 seconds
Domain Reload Profiling: 1469ms
	BeginReloadAssembly (120ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (234ms)
		LoadAssemblies (274ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1048ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (507ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (108ms)
			ProcessInitializeOnLoadAttributes (330ms)
			ProcessInitializeOnLoadMethodAttributes (59ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 8.16 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5968 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.7 KB). Loaded Objects now: 6750.
Memory consumption went from 206.0 MB to 205.7 MB.
Total: 6.132500 ms (FindLiveObjects: 0.623600 ms CreateObjectMapping: 0.320900 ms MarkObjects: 5.095500 ms  DeleteObjects: 0.091500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemTest.cs: 6e02a73794208c8111900b7754ef579a -> 
  custom:scripting/monoscript/fileName/StorageCachePerformanceTest.cs: 810a91afa3433152557aa6d0203dc5c7 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/StorageCacheV2.cs: 8022bfbbf077d75662294ee2ce0494c5 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:scripting/monoscript/fileName/StorageTypeConverter.cs: ced5c46ed1b2bcfa05b15566d0dd740e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageTypeWrapper.cs: 8f3fe27bd2c7f3fdf210ab110b81d87c -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/StorageType_string.cs: f1c0ee407fa20bb6a3f55743ae44bc01 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/StorageType_float.cs: 910016c00f4bc21d02b861fd3ad6cff4 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/StorageTypeMgr.cs: e109819f414051191819c259cf31e28b -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemExample.cs: dc415107117cba3a2df55398f422588b -> 
  custom:scripting/monoscript/fileName/StorageType_int.cs: c52d6ca8d70163e6abd6c217a3751919 -> 
  custom:scripting/monoscript/fileName/StorageType_bool.cs: 9818a5a72b020d32a15f7b96f088344e -> 
  custom:scripting/monoscript/fileName/StorageType_Primitives.cs: 9ae0bf72a9f391e65078ecd7c13cc164 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/StorageType_Unity.cs: b84105f2d8f5844ecdec8f7f9d0a0b05 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: fb9670a3f86a01551d45fde1f181c407 -> e3b0619a2e8f8221a937ec724b8b71bb
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.544 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.253 seconds
Domain Reload Profiling: 1797ms
	BeginReloadAssembly (138ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (37ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (46ms)
	LoadAllAssembliesAndSetupDomain (309ms)
		LoadAssemblies (313ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (58ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (28ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1254ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (653ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (117ms)
			ProcessInitializeOnLoadAttributes (449ms)
			ProcessInitializeOnLoadMethodAttributes (76ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.17 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5969 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.7 KB). Loaded Objects now: 6757.
Memory consumption went from 206.1 MB to 205.8 MB.
Total: 3.405200 ms (FindLiveObjects: 0.515800 ms CreateObjectMapping: 0.156900 ms MarkObjects: 2.652100 ms  DeleteObjects: 0.079400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemTest.cs: 6e02a73794208c8111900b7754ef579a -> 
  custom:scripting/monoscript/fileName/StorageCachePerformanceTest.cs: 810a91afa3433152557aa6d0203dc5c7 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/StorageCacheV2.cs: 8022bfbbf077d75662294ee2ce0494c5 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:scripting/monoscript/fileName/StorageTypeConverter.cs: ced5c46ed1b2bcfa05b15566d0dd740e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageTypeWrapper.cs: 8f3fe27bd2c7f3fdf210ab110b81d87c -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/StorageType_string.cs: f1c0ee407fa20bb6a3f55743ae44bc01 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/StorageType_float.cs: 910016c00f4bc21d02b861fd3ad6cff4 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/StorageTypeMgr.cs: e109819f414051191819c259cf31e28b -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemExample.cs: dc415107117cba3a2df55398f422588b -> 
  custom:scripting/monoscript/fileName/StorageType_int.cs: c52d6ca8d70163e6abd6c217a3751919 -> 
  custom:scripting/monoscript/fileName/StorageType_bool.cs: 9818a5a72b020d32a15f7b96f088344e -> 
  custom:scripting/monoscript/fileName/StorageTypeMgrTest.cs: 1ea03b4c286255e5fd2b6de4e9f86354 -> 
  custom:scripting/monoscript/fileName/StorageType_Primitives.cs: 9ae0bf72a9f391e65078ecd7c13cc164 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/StorageType_Unity.cs: b84105f2d8f5844ecdec8f7f9d0a0b05 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: ba2c480a6ecabbe4a896525277aac757 -> e3b0619a2e8f8221a937ec724b8b71bb
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.440 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.041 seconds
Domain Reload Profiling: 1481ms
	BeginReloadAssembly (121ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (250ms)
		LoadAssemblies (271ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (36ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (16ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1041ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (500ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (114ms)
			ProcessInitializeOnLoadAttributes (323ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 7.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5968 Unused Serialized files (Serialized files now loaded: 0)
Unloading 31 unused Assets / (320.2 KB). Loaded Objects now: 6762.
Memory consumption went from 206.1 MB to 205.8 MB.
Total: 3.375400 ms (FindLiveObjects: 0.349800 ms CreateObjectMapping: 0.262200 ms MarkObjects: 2.682300 ms  DeleteObjects: 0.080300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemTest.cs: 6e02a73794208c8111900b7754ef579a -> 
  custom:scripting/monoscript/fileName/StorageCachePerformanceTest.cs: 810a91afa3433152557aa6d0203dc5c7 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/StorageCacheV2.cs: 8022bfbbf077d75662294ee2ce0494c5 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:scripting/monoscript/fileName/StorageTypeConverter.cs: ced5c46ed1b2bcfa05b15566d0dd740e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageTypeWrapper.cs: 8f3fe27bd2c7f3fdf210ab110b81d87c -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/StorageType_string.cs: f1c0ee407fa20bb6a3f55743ae44bc01 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/StorageType_float.cs: 910016c00f4bc21d02b861fd3ad6cff4 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/StorageTypeMgr.cs: e109819f414051191819c259cf31e28b -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemExample.cs: dc415107117cba3a2df55398f422588b -> 
  custom:scripting/monoscript/fileName/StorageType_int.cs: c52d6ca8d70163e6abd6c217a3751919 -> 
  custom:scripting/monoscript/fileName/StorageType_bool.cs: 9818a5a72b020d32a15f7b96f088344e -> 
  custom:scripting/monoscript/fileName/StorageTypeMgrTest.cs: 1ea03b4c286255e5fd2b6de4e9f86354 -> 
  custom:scripting/monoscript/fileName/StorageType_Primitives.cs: 9ae0bf72a9f391e65078ecd7c13cc164 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/StorageType_Unity.cs: b84105f2d8f5844ecdec8f7f9d0a0b05 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: fb9670a3f86a01551d45fde1f181c407 -> e3b0619a2e8f8221a937ec724b8b71bb
========================================================================
Received Prepare
Caller must complete domain reload
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.411 seconds
Native extension for WindowsStandalone target not found
[MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpUtils:GetServerPath () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs:104)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:151)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/McpUtils.cs Line: 104)

[MCP Unity] Server path not found or invalid: [MCP Unity] Could not locate Server directory. Please check the installation of the MCP Unity package.. Make sure that MCP Node.js server is installed.
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:InstallServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:155)
McpUnity.Unity.McpUnityServer:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:79)
McpUnity.Unity.McpUnityServer:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:58)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:40)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 155)

[MCP Unity] Updated workspace configuration in H:\DiceGame\DGame\DGame.code-workspace
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: 通常每个套接字地址(协议/网络地址/端口)只允许使用一次。
UnityEngine.StackTraceUtility:ExtractStackTrace ()
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:106)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs:45)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[])

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@1aaca031af/Editor/UnityBridge/McpUnityServer.cs Line: 106)

Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.056 seconds
Domain Reload Profiling: 1466ms
	BeginReloadAssembly (117ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (226ms)
		LoadAssemblies (265ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1057ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (528ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (102ms)
			ProcessInitializeOnLoadAttributes (355ms)
			ProcessInitializeOnLoadMethodAttributes (60ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5968 Unused Serialized files (Serialized files now loaded: 0)
Unloading 30 unused Assets / (319.6 KB). Loaded Objects now: 6768.
Memory consumption went from 206.1 MB to 205.8 MB.
Total: 4.954900 ms (FindLiveObjects: 0.665200 ms CreateObjectMapping: 0.212400 ms MarkObjects: 3.996200 ms  DeleteObjects: 0.080400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemTest.cs: 6e02a73794208c8111900b7754ef579a -> 
  custom:scripting/monoscript/fileName/StorageCachePerformanceTest.cs: 810a91afa3433152557aa6d0203dc5c7 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/StorageCacheV2.cs: 8022bfbbf077d75662294ee2ce0494c5 -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:scripting/monoscript/fileName/StorageTypeConverter.cs: ced5c46ed1b2bcfa05b15566d0dd740e -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/StorageTypeWrapper.cs: 8f3fe27bd2c7f3fdf210ab110b81d87c -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:scripting/monoscript/fileName/StorageType_string.cs: f1c0ee407fa20bb6a3f55743ae44bc01 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:scripting/monoscript/fileName/StorageType_float.cs: 910016c00f4bc21d02b861fd3ad6cff4 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:scripting/monoscript/fileName/StorageTypeMgr.cs: e109819f414051191819c259cf31e28b -> 
  custom:scripting/monoscript/fileName/StorageTypeSystemExample.cs: dc415107117cba3a2df55398f422588b -> 
  custom:scripting/monoscript/fileName/StorageType_int.cs: c52d6ca8d70163e6abd6c217a3751919 -> 
  custom:scripting/monoscript/fileName/StorageType_bool.cs: 9818a5a72b020d32a15f7b96f088344e -> 
  custom:scripting/monoscript/fileName/StorageTypeMgrTest.cs: 1ea03b4c286255e5fd2b6de4e9f86354 -> 
  custom:scripting/monoscript/fileName/StorageType_Primitives.cs: 9ae0bf72a9f391e65078ecd7c13cc164 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/StorageType_Unity.cs: b84105f2d8f5844ecdec8f7f9d0a0b05 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/precompiled-assembly-types:Assembly-CSharp: fb9670a3f86a01551d45fde1f181c407 -> e3b0619a2e8f8221a937ec724b8b71bb
