using System;
using System.Collections.Generic;
using DGame.Framework;
using Storage.Serialization;

namespace Storage
{
    /// <summary>
    /// 存储系统的内存缓存管理器，负责管理内存中的数据
    /// 使用类型包装器系统避免值类型的装箱拆箱操作，提高性能并减少GC压力
    /// </summary>
    public class StorageCache
    {
        #region 私有字段

        // 使用StorageTypeWrapper避免装箱拆箱操作
        private readonly Dictionary<string, StorageTypeWrapper> _cache;
        private bool _isDirty = false;

        #endregion

        #region 属性
        /// <summary>
        /// 缓存是否已被修改
        /// </summary>
        public bool IsDirty
        {
            get { return _isDirty; }
        }

        /// <summary>
        /// 缓存中的数据数量
        /// </summary>
        public int Count
        {
            get { return _cache.Count; }
        }

        /// <summary>
        /// 获取所有的键
        /// </summary>
        public ICollection<string> Keys
        {
            get { return _cache.Keys; }
        }

        #endregion

        #region 构造函数
        /// <summary>
        /// 默认构造函数
        /// </summary>
        public StorageCache()
        {
            _cache = new Dictionary<string, StorageTypeWrapper>();

            // 确保类型管理器已初始化
            StorageTypeMgr.Initialize();
        }

        /// <summary>
        /// 带初始容量的构造函数
        /// </summary>
        /// <param name="initialCapacity">初始容量</param>
        public StorageCache(int initialCapacity)
        {
            _cache = new Dictionary<string, StorageTypeWrapper>(initialCapacity);

            // 确保类型管理器已初始化
            StorageTypeMgr.Initialize();
        }

        #endregion

        // 注意：类型转换逻辑已移至StorageTypeConverter类中

        #region 数据操作方法

        /// <summary>
        /// 设置数据（使用类型包装器避免装箱）
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        public void Set<T>(string key, T value)
        {
            if (_cache.TryGetValue(key, out var existingWrapper))
            {
                // 如果类型匹配，直接设置值（避免重新创建包装器）
                if (existingWrapper.GetValueType() == typeof(T))
                {
                    existingWrapper.SetValue(value);
                }
                else
                {
                    // 类型不匹配，创建新的包装器
                    _cache[key] = StorageTypeMgr.CreateWrapper(value);
                }
            }
            else
            {
                // 新键，创建包装器
                _cache[key] = StorageTypeMgr.CreateWrapper(value);
            }
            _isDirty = true;
        }

        /// <summary>
        /// 尝试获取数据（使用类型包装器避免拆箱）
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">输出的数据值</param>
        /// <returns>是否成功获取到值</returns>
        public bool TryGet<T>(string key, out T value)
        {
            value = default(T);

            if (_cache.TryGetValue(key, out var wrapper))
            {
                try
                {
                    // 使用包装器的类型安全方法获取值
                    if (wrapper.TryGetValue(out value))
                    {
                        return true;
                    }

                    NLogger.LogWarning("Cannot convert value for key '{0}' from type {1} to type {2}. Cached value: {3}",
                        arg0: key, arg1: wrapper.GetValueType().Name, arg2: typeof(T).Name, arg3: wrapper.GetValueString());
                    return false;
                }
                catch (Exception ex)
                {
                    NLogger.LogError("Error getting value for key '{0}': {1}", arg0: key, arg1: ex.Message);
                    return false;
                }
            }

            NLogger.LogWarning("Key '{0}' not found", arg0: key);
            return false;
        }

        /// <summary>
        /// 检查键是否存在
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否存在</returns>
        public bool ContainsKey(string key)
        {
            return _cache.ContainsKey(key);
        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否删除成功</returns>
        public bool Remove(string key)
        {
            bool removed = _cache.Remove(key);
            if (removed)
            {
                _isDirty = true;
                NLogger.Log("Key '{0}' removed successfully", arg0: key);
            }
            else
            {
                NLogger.LogWarning("Key '{0}' not found", arg0: key);
            }
            return removed;
        }

        /// <summary>
        /// 清空所有数据
        /// </summary>
        public void Clear()
        {
            int count = _cache.Count;
            _cache.Clear();
            _isDirty = true;
            NLogger.Log("Cleared {0} items from cache", arg0: count);
        }

        /// <summary>
        /// 获取所有数据的副本（用于序列化，此时才进行装箱）
        /// </summary>
        /// <returns>数据字典的副本</returns>
        public Dictionary<string, object> GetAllData()
        {
            var result = new Dictionary<string, object>();
            foreach (var kvp in _cache)
            {
                result[kvp.Key] = kvp.Value.GetBoxedValue();
            }
            return result;
        }

        /// <summary>
        /// 从字典加载数据（从序列化数据恢复，此时进行装箱但创建类型包装器）
        /// </summary>
        /// <param name="data">数据字典</param>
        public void LoadFromDictionary(Dictionary<string, object> data)
        {
            if (data == null)
            {
                NLogger.LogError("Data dictionary cannot be null");
                return;
            }

            _cache.Clear();
            foreach (var kvp in data)
            {
                // 根据值的类型创建合适的包装器
                _cache[kvp.Key] = StorageTypeMgr.CreateWrapperFromValue(kvp.Value);
            }
            _isDirty = false;
            NLogger.Log("Loaded {0} items into cache", arg0: data.Count);
        }

        /// <summary>
        /// 标记缓存为已保存状态
        /// </summary>
        public void MarkAsSaved()
        {
            _isDirty = false;
        }

        /// <summary>
        /// 强制标记缓存为脏状态
        /// </summary>
        public void MarkAsDirty()
        {
            _isDirty = true;
        }

        #endregion

        #region 调试方法

        /// <summary>
        /// 获取缓存状态信息
        /// </summary>
        /// <returns>状态信息字符串</returns>
        public string GetStatusInfo()
        {
            return $"StorageCache Status - Count: {Count}, IsDirty: {IsDirty}";
        }

        /// <summary>
        /// 打印所有缓存的键
        /// </summary>
        public void LogAllKeys()
        {
            var keys = string.Join(", ", Keys);
            NLogger.Log("All Keys: [{0}]", arg0: keys);
        }

        #endregion
    }
}
