using System;
using UnityEngine;
using Newtonsoft.Json.Linq;
using DGame.Framework;
using Storage.Serialization;

namespace Storage
{
    /// <summary>
    /// 存储类型转换工具类
    /// 提供各种类型之间的转换功能，保持与原StorageCache相同的转换能力
    /// </summary>
    public static class StorageTypeConverter
    {
        /// <summary>
        /// 检查是否可以从源类型转换到目标类型
        /// </summary>
        /// <param name="fromType">源类型</param>
        /// <param name="toType">目标类型</param>
        /// <returns>是否可以转换</returns>
        public static bool CanConvert(Type fromType, Type toType)
        {
            if (fromType == toType)
                return true;

            // 处理可空类型
            var underlyingToType = Nullable.GetUnderlyingType(toType);
            if (underlyingToType != null)
                toType = underlyingToType;

            // 数值类型之间可以转换
            if (IsNumericType(fromType) && IsNumericType(toType))
                return true;

            // 枚举类型转换
            if (toType.IsEnum && (fromType == typeof(string) || IsNumericType(fromType)))
                return true;

            // 字符串转换
            if (toType == typeof(string))
                return true;

            // BigInteger转换
            if (fromType.Name == "BigInteger" && IsNumericType(toType))
                return true;

            // JObject转换
            if (fromType.Name == "JObject")
                return true;

            // 基础类型转换
            if (toType.IsPrimitive || toType == typeof(decimal) || toType == typeof(DateTime) || toType == typeof(Guid))
                return true;

            return false;
        }

        /// <summary>
        /// 尝试将值从一种类型转换为另一种类型
        /// </summary>
        /// <typeparam name="TFrom">源类型</typeparam>
        /// <typeparam name="TTo">目标类型</typeparam>
        /// <param name="fromValue">源值</param>
        /// <param name="toValue">转换后的值</param>
        /// <returns>是否转换成功</returns>
        public static bool TryConvert<TFrom, TTo>(TFrom fromValue, out TTo toValue)
        {
            toValue = default(TTo);

            if (fromValue == null)
            {
                return !typeof(TTo).IsValueType || Nullable.GetUnderlyingType(typeof(TTo)) != null;
            }

            var targetType = typeof(TTo);
            var sourceType = typeof(TFrom);

            // 如果类型完全匹配，直接返回
            if (sourceType == targetType)
            {
                toValue = (TTo)(object)fromValue;
                return true;
            }

            // 处理可空类型
            var underlyingType = Nullable.GetUnderlyingType(targetType);
            if (underlyingType != null)
            {
                targetType = underlyingType;
            }

            try
            {
                // 处理BigInteger到数值类型的转换（JSON反序列化大数值问题）
                if (sourceType.Name == "BigInteger" && IsNumericType(targetType))
                {
                    return TryConvertFromBigInteger(fromValue, targetType, out toValue);
                }

                // 处理JObject到Unity类型的转换（JSON反序列化Unity类型问题）
                if (sourceType.Name == "JObject" && fromValue is JObject jObject)
                {
                    return TryConvertFromJObject(jObject, out toValue);
                }

                // 处理数值类型的转换（JSON反序列化常见问题）
                if (IsNumericType(sourceType) && IsNumericType(targetType))
                {
                    toValue = (TTo)Convert.ChangeType(fromValue, targetType);
                    return true;
                }

                // 处理枚举类型
                if (targetType.IsEnum)
                {
                    return TryConvertToEnum(fromValue, targetType, out toValue);
                }

                // 处理字符串转换
                if (targetType == typeof(string))
                {
                    toValue = (TTo)(object)fromValue.ToString();
                    return true;
                }

                // 使用Convert.ChangeType进行通用转换
                if (targetType.IsPrimitive || targetType == typeof(decimal) || targetType == typeof(DateTime) || targetType == typeof(Guid))
                {
                    toValue = (TTo)Convert.ChangeType(fromValue, targetType);
                    return true;
                }
            }
            catch (Exception ex)
            {
                NLogger.LogWarning("Type conversion failed from {0} to {1}: {2}",
                    arg0: sourceType.Name, arg1: targetType.Name, arg2: ex.Message);
            }

            return false;
        }

        /// <summary>
        /// 检查是否为数值类型
        /// </summary>
        /// <param name="type">要检查的类型</param>
        /// <returns>是否为数值类型</returns>
        private static bool IsNumericType(Type type)
        {
            return type == typeof(byte) || type == typeof(sbyte) ||
                type == typeof(short) || type == typeof(ushort) ||
                type == typeof(int) || type == typeof(uint) ||
                type == typeof(long) || type == typeof(ulong) ||
                type == typeof(float) || type == typeof(double) ||
                type == typeof(decimal);
        }

        /// <summary>
        /// 尝试从BigInteger转换到数值类型
        /// </summary>
        private static bool TryConvertFromBigInteger<TTo>(object bigIntValue, Type targetType, out TTo toValue)
        {
            toValue = default(TTo);

            try
            {
                // 使用反射获取BigInteger的值并转换
                var bigIntString = bigIntValue.ToString();

                if (targetType == typeof(ulong))
                {
                    if (ulong.TryParse(bigIntString, out ulong ulongResult))
                    {
                        toValue = (TTo)(object)ulongResult;
                        return true;
                    }
                }
                else if (targetType == typeof(long))
                {
                    if (long.TryParse(bigIntString, out long longResult))
                    {
                        toValue = (TTo)(object)longResult;
                        return true;
                    }
                }

                // 其他数值类型也可以通过Convert.ChangeType处理
                toValue = (TTo)Convert.ChangeType(Convert.ToDecimal(bigIntString), targetType);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 尝试从JObject转换到目标类型
        /// </summary>
        private static bool TryConvertFromJObject<TTo>(JObject jObject, out TTo toValue)
        {
            toValue = default(TTo);

            try
            {
                // 使用StorageSerializer重新序列化和反序列化
                string json = jObject.ToString();
                var convertedValue = StorageSerializer.DeserializeObject<TTo>(json);
                if (convertedValue != null)
                {
                    toValue = convertedValue;
                    return true;
                }
            }
            catch
            {
                // 转换失败，返回false
            }

            return false;
        }

        /// <summary>
        /// 尝试转换到枚举类型
        /// </summary>
        private static bool TryConvertToEnum<TTo>(object value, Type enumType, out TTo toValue)
        {
            toValue = default(TTo);

            try
            {
                var sourceType = value.GetType();

                if (sourceType == typeof(string))
                {
                    toValue = (TTo)Enum.Parse(enumType, (string)value);
                    return true;
                }
                else if (IsNumericType(sourceType))
                {
                    toValue = (TTo)Enum.ToObject(enumType, value);
                    return true;
                }
            }
            catch
            {
                // 转换失败，返回false
            }

            return false;
        }
    }
}
