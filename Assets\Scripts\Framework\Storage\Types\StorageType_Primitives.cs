using System;
using System.Runtime.CompilerServices;

namespace Storage
{
    /// <summary>
    /// 基础类型的特化包装器实现
    /// 提供高性能的值类型存储，避免装箱拆箱操作
    /// </summary>
    
    #region 数值类型特化包装器

    /// <summary>
    /// int类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_int : StorageTypeWrapper
    {
        private int _value;

        public StorageTypeWrapper_int() { }
        public StorageTypeWrapper_int(int value) { _value = value; }

        public override Type GetValueType() => typeof(int);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(int))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(int))
            {
                _value = (int)(object)value;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out int converted))
                _value = converted;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_int();
        public override string GetValueString() => _value.ToString();
        
        public int GetValue() => _value;
        public void SetValue(int value) => _value = value;
    }

    /// <summary>
    /// float类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_float : StorageTypeWrapper
    {
        private float _value;

        public StorageTypeWrapper_float() { }
        public StorageTypeWrapper_float(float value) { _value = value; }

        public override Type GetValueType() => typeof(float);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(float))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(float))
            {
                _value = (float)(object)value;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out float converted))
                _value = converted;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_float();
        public override string GetValueString() => _value.ToString("F6");
        
        public float GetValue() => _value;
        public void SetValue(float value) => _value = value;
    }

    /// <summary>
    /// bool类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_bool : StorageTypeWrapper
    {
        private bool _value;

        public StorageTypeWrapper_bool() { }
        public StorageTypeWrapper_bool(bool value) { _value = value; }

        public override Type GetValueType() => typeof(bool);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(bool))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(bool))
            {
                _value = (bool)(object)value;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out bool converted))
                _value = converted;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_bool();
        public override string GetValueString() => _value.ToString().ToLower();
        
        public bool GetValue() => _value;
        public void SetValue(bool value) => _value = value;
    }

    /// <summary>
    /// long类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_long : StorageTypeWrapper
    {
        private long _value;

        public StorageTypeWrapper_long() { }
        public StorageTypeWrapper_long(long value) { _value = value; }

        public override Type GetValueType() => typeof(long);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(long))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(long))
            {
                _value = (long)(object)value;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out long converted))
                _value = converted;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_long();
        public override string GetValueString() => _value.ToString();
        
        public long GetValue() => _value;
        public void SetValue(long value) => _value = value;
    }

    /// <summary>
    /// double类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_double : StorageTypeWrapper
    {
        private double _value;

        public StorageTypeWrapper_double() { }
        public StorageTypeWrapper_double(double value) { _value = value; }

        public override Type GetValueType() => typeof(double);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(double))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(double))
            {
                _value = (double)(object)value;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out double converted))
                _value = converted;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_double();
        public override string GetValueString() => _value.ToString("F15");
        
        public double GetValue() => _value;
        public void SetValue(double value) => _value = value;
    }

    #endregion

    #region 字符串类型特化包装器

    /// <summary>
    /// string类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_string : StorageTypeWrapper
    {
        private string _value;

        public StorageTypeWrapper_string() { _value = string.Empty; }
        public StorageTypeWrapper_string(string value) { _value = value ?? string.Empty; }

        public override Type GetValueType() => typeof(string);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(string))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(string))
            {
                _value = (string)(object)value ?? string.Empty;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out string converted))
                _value = converted ?? string.Empty;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_string();
        public override string GetValueString() => $"\"{_value}\"";
        
        public string GetValue() => _value;
        public void SetValue(string value) => _value = value ?? string.Empty;
    }

    #endregion
}
