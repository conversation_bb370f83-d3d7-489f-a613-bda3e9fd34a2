using System;
using DGame.Framework;
using UnityEngine;

namespace Storage
{
    /// <summary>
    /// 存储类型包装器基类
    /// 用于避免值类型的装箱拆箱操作，提高性能并减少GC压力
    /// </summary>
    public abstract class StorageTypeWrapper
    {
        /// <summary>
        /// 获取包装的值类型
        /// </summary>
        /// <returns>值的类型</returns>
        public abstract Type GetValueType();

        /// <summary>
        /// 尝试获取指定类型的值
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="value">输出的值</param>
        /// <returns>是否成功获取</returns>
        public abstract bool TryGetValue<T>(out T value);

        /// <summary>
        /// 设置值
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="value">要设置的值</param>
        public abstract void SetValue<T>(T value);

        /// <summary>
        /// 获取装箱的值（仅用于序列化）
        /// </summary>
        /// <returns>装箱的值</returns>
        public abstract object GetBoxedValue();

        /// <summary>
        /// 创建相同类型的新包装器实例
        /// </summary>
        /// <returns>新的包装器实例</returns>
        public abstract StorageTypeWrapper CreateInstance();

        /// <summary>
        /// 检查是否可以转换为指定类型
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <returns>是否可以转换</returns>
        public virtual bool CanConvertTo<T>()
        {
            return StorageTypeConverter.CanConvert(GetValueType(), typeof(T));
        }

        /// <summary>
        /// 获取值的字符串表示（用于日志）
        /// </summary>
        /// <returns>值的字符串表示</returns>
        public abstract string GetValueString();
    }

    /// <summary>
    /// 泛型类型包装器，直接存储值类型以避免装箱
    /// </summary>
    /// <typeparam name="TValue">包装的值类型</typeparam>
    public class StorageTypeWrapper<TValue> : StorageTypeWrapper
    {
        private TValue _value;

        /// <summary>
        /// 构造函数
        /// </summary>
        public StorageTypeWrapper()
        {
            _value = default(TValue);
        }

        /// <summary>
        /// 带初始值的构造函数
        /// </summary>
        /// <param name="value">初始值</param>
        public StorageTypeWrapper(TValue value)
        {
            _value = value;
        }

        /// <summary>
        /// 获取包装的值类型
        /// </summary>
        /// <returns>值的类型</returns>
        public override Type GetValueType()
        {
            return typeof(TValue);
        }

        /// <summary>
        /// 尝试获取指定类型的值
        /// </summary>
        /// <typeparam name="T">目标类型</typeparam>
        /// <param name="value">输出的值</param>
        /// <returns>是否成功获取</returns>
        public override bool TryGetValue<T>(out T value)
        {
            // 如果类型完全匹配，直接返回（编译器会优化掉装箱操作）
            if (typeof(T) == typeof(TValue))
            {
                value = (T)(object)_value;
                return true;
            }

            // 尝试类型转换
            if (StorageTypeConverter.TryConvert(_value, out T convertedValue))
            {
                value = convertedValue;
                return true;
            }

            value = default(T);
            return false;
        }

        /// <summary>
        /// 设置值
        /// </summary>
        /// <typeparam name="T">值类型</typeparam>
        /// <param name="value">要设置的值</param>
        public override void SetValue<T>(T value)
        {
            // 如果类型完全匹配，直接设置
            if (typeof(T) == typeof(TValue))
            {
                _value = (TValue)(object)value;
                return;
            }

            // 尝试类型转换
            if (StorageTypeConverter.TryConvert(value, out TValue convertedValue))
            {
                _value = convertedValue;
                return;
            }

            NLogger.LogError("Cannot convert value of type {0} to {1}", arg0: typeof(T).Name, arg1: typeof(TValue).Name);
        }

        /// <summary>
        /// 获取装箱的值（仅用于序列化）
        /// </summary>
        /// <returns>装箱的值</returns>
        public override object GetBoxedValue()
        {
            return _value;
        }

        /// <summary>
        /// 创建相同类型的新包装器实例
        /// </summary>
        /// <returns>新的包装器实例</returns>
        public override StorageTypeWrapper CreateInstance()
        {
            return new StorageTypeWrapper<TValue>();
        }

        /// <summary>
        /// 获取值的字符串表示（用于日志）
        /// </summary>
        /// <returns>值的字符串表示</returns>
        public override string GetValueString()
        {
            if (_value == null)
            {
                return "null";
            }

            // 对于字符串类型，添加引号
            if (typeof(TValue) == typeof(string))
            {
                return $"\"{_value}\"";
            }

            return _value.ToString();
        }

        /// <summary>
        /// 直接获取值（类型安全）
        /// </summary>
        /// <returns>存储的值</returns>
        public TValue GetValue()
        {
            return _value;
        }

        /// <summary>
        /// 直接设置值（类型安全）
        /// </summary>
        /// <param name="value">要设置的值</param>
        public void SetValue(TValue value)
        {
            _value = value;
        }
    }
}
