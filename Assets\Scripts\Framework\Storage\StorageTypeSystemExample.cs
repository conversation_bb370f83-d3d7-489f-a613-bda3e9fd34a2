using UnityEngine;
using DGame.Framework;

namespace Storage
{
    /// <summary>
    /// 存储类型系统使用示例
    /// 展示新的类型包装器系统如何避免装箱拆箱操作
    /// </summary>
    public class StorageTypeSystemExample : MonoBehaviour
    {
        [Header("示例配置")]
        [SerializeField] private bool _runOnStart = true;
        [SerializeField] private bool _showDetailedLogs = true;

        private StorageCache _cache;

        #region Unity生命周期

        private void Start()
        {
            if (_runOnStart)
            {
                RunExample();
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 运行类型系统示例
        /// </summary>
        [ContextMenu("运行类型系统示例")]
        public void RunExample()
        {
            NLogger.Log("=== 存储类型系统示例开始 ===");

            // 初始化缓存
            _cache = new StorageCache();

            // 演示基础类型的零装箱操作
            DemonstrateBasicTypes();

            // 演示Unity类型的零装箱操作
            DemonstrateUnityTypes();

            // 演示类型转换功能
            DemonstrateTypeConversion();

            // 演示性能优势
            DemonstratePerformanceAdvantage();

            NLogger.Log("=== 存储类型系统示例完成 ===");
        }

        #endregion

        #region 示例方法

        /// <summary>
        /// 演示基础类型的零装箱操作
        /// </summary>
        private void DemonstrateBasicTypes()
        {
            NLogger.Log("--- 基础类型零装箱演示 ---");

            // int类型 - 使用特化包装器，完全避免装箱
            _cache.Set("player_level", 42);
            if (_cache.TryGet("player_level", out int level))
            {
                NLogger.Log("玩家等级: {0} (类型: int, 无装箱)", arg0: level);
            }

            // float类型 - 使用特化包装器，完全避免装箱
            _cache.Set("player_health", 95.5f);
            if (_cache.TryGet("player_health", out float health))
            {
                NLogger.Log("玩家血量: {0:F1} (类型: float, 无装箱)", arg0: health);
            }

            // bool类型 - 使用特化包装器，完全避免装箱
            _cache.Set("is_premium", true);
            if (_cache.TryGet("is_premium", out bool isPremium))
            {
                NLogger.Log("是否高级用户: {0} (类型: bool, 无装箱)", arg0: isPremium);
            }

            // string类型 - 使用特化包装器，引用类型无装箱问题
            _cache.Set("player_name", "TestPlayer");
            if (_cache.TryGet("player_name", out string playerName))
            {
                NLogger.Log("玩家名称: {0} (类型: string, 引用类型)", arg0: playerName);
            }

            // long类型 - 使用特化包装器，完全避免装箱
            _cache.Set("total_score", 1234567890L);
            if (_cache.TryGet("total_score", out long score))
            {
                NLogger.Log("总分: {0} (类型: long, 无装箱)", arg0: score);
            }
        }

        /// <summary>
        /// 演示Unity类型的零装箱操作
        /// </summary>
        private void DemonstrateUnityTypes()
        {
            NLogger.Log("--- Unity类型零装箱演示 ---");

            // Vector3类型 - 使用特化包装器，完全避免装箱
            Vector3 playerPosition = new Vector3(10.5f, 2.0f, -5.3f);
            _cache.Set("player_position", playerPosition);
            if (_cache.TryGet("player_position", out Vector3 position))
            {
                NLogger.Log("玩家位置: {0} (类型: Vector3, 无装箱)", arg0: position);
            }

            // Quaternion类型 - 使用特化包装器，完全避免装箱
            Quaternion playerRotation = Quaternion.Euler(0, 45, 0);
            _cache.Set("player_rotation", playerRotation);
            if (_cache.TryGet("player_rotation", out Quaternion rotation))
            {
                NLogger.Log("玩家旋转: {0} (类型: Quaternion, 无装箱)", arg0: rotation.eulerAngles);
            }

            // Color类型 - 使用特化包装器，完全避免装箱
            Color playerColor = new Color(1.0f, 0.5f, 0.2f, 1.0f);
            _cache.Set("player_color", playerColor);
            if (_cache.TryGet("player_color", out Color color))
            {
                NLogger.Log("玩家颜色: {0} (类型: Color, 无装箱)", arg0: color);
            }

            // Vector2类型 - 使用特化包装器，完全避免装箱
            Vector2 screenPosition = new Vector2(800, 600);
            _cache.Set("screen_position", screenPosition);
            if (_cache.TryGet("screen_position", out Vector2 screenPos))
            {
                NLogger.Log("屏幕位置: {0} (类型: Vector2, 无装箱)", arg0: screenPos);
            }
        }

        /// <summary>
        /// 演示类型转换功能
        /// </summary>
        private void DemonstrateTypeConversion()
        {
            NLogger.Log("--- 类型转换功能演示 ---");

            // 存储int，读取为float（自动转换）
            _cache.Set("convertible_number", 42);
            if (_cache.TryGet("convertible_number", out float floatValue))
            {
                NLogger.Log("int转float: 42 -> {0:F1}", arg0: floatValue);
            }

            // 存储float，读取为double（自动转换）
            _cache.Set("precise_value", 3.14159f);
            if (_cache.TryGet("precise_value", out double doubleValue))
            {
                NLogger.Log("float转double: 3.14159f -> {0:F5}", arg0: doubleValue);
            }

            // 存储bool，读取为string（自动转换）
            _cache.Set("bool_flag", true);
            if (_cache.TryGet("bool_flag", out string boolString))
            {
                NLogger.Log("bool转string: true -> \"{0}\"", arg0: boolString);
            }

            // 演示转换失败的情况
            _cache.Set("text_value", "Hello World");
            if (!_cache.TryGet("text_value", out int invalidConversion))
            {
                NLogger.LogWarning("预期的转换失败: string \"Hello World\" 无法转换为 int");
            }
        }

        /// <summary>
        /// 演示性能优势
        /// </summary>
        private void DemonstratePerformanceAdvantage()
        {
            NLogger.Log("--- 性能优势演示 ---");

            const int iterations = 10000;

            // 测试频繁的值类型操作
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            for (int i = 0; i < iterations; i++)
            {
                // 这些操作完全避免装箱拆箱
                _cache.Set($"perf_int_{i % 100}", i);
                _cache.Set($"perf_float_{i % 100}", i * 0.1f);
                _cache.Set($"perf_vector_{i % 100}", new Vector3(i, i, i));

                // 读取操作也避免拆箱
                _cache.TryGet($"perf_int_{i % 100}", out int _);
                _cache.TryGet($"perf_float_{i % 100}", out float _);
                _cache.TryGet($"perf_vector_{i % 100}", out Vector3 _);
            }

            stopwatch.Stop();

            NLogger.Log("完成 {0} 次零装箱操作，耗时: {1}ms",
                arg0: iterations * 6, arg1: stopwatch.ElapsedMilliseconds);

            // 显示内存使用情况
            long memoryBefore = System.GC.GetTotalMemory(false);
            System.GC.Collect();
            System.GC.WaitForPendingFinalizers();
            System.GC.Collect();
            long memoryAfter = System.GC.GetTotalMemory(true);

            NLogger.Log("GC前内存: {0} KB, GC后内存: {1} KB, 回收: {2} KB",
                arg0: memoryBefore / 1024, arg1: memoryAfter / 1024, arg2: (memoryBefore - memoryAfter) / 1024);
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 清理缓存
        /// </summary>
        [ContextMenu("清理缓存")]
        public void ClearCache()
        {
            _cache?.Clear();
            NLogger.Log("缓存已清理");
        }

        /// <summary>
        /// 显示缓存统计信息
        /// </summary>
        [ContextMenu("显示缓存统计")]
        public void ShowCacheStats()
        {
            if (_cache != null)
            {
                NLogger.Log("缓存项数量: {0}", arg0: _cache.Count);
                NLogger.Log("缓存是否脏: {0}", arg0: _cache.IsDirty);

                if (_showDetailedLogs)
                {
                    var keys = _cache.GetAllData().Keys;
                    NLogger.Log("缓存键列表: {0}", arg0: string.Join(", ", keys));
                }
            }
            else
            {
                NLogger.LogWarning("缓存未初始化");
            }
        }

        #endregion
    }
}
