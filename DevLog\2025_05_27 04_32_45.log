2025-05-27 04:32:45.4583 [INFO] [Storage]::InitializeCore(68) - Storage core initialized successfully
2025-05-27 04:32:45.4783 [INFO] [StorageSettings]::InitializePathCache(71) - Storage paths cached successfully
2025-05-27 04:32:45.4783 [INFO] [StorageManager]::Initialize(80) - StorageManager initialized successfully
2025-05-27 04:32:45.4783 [INFO] [StorageTypeMgr]::Initialize(54) - StorageTypeMgr initialized with 36 registered types
2025-05-27 04:32:45.4783 [INFO] [StorageInstance]::.ctor(72) - Storage instance created: ConcurrencyTest
2025-05-27 04:32:45.4783 [INFO] [StorageConcurrencyTest]::RunConcurrencyTests(45) - === 开始存储并发安全测试 ===
2025-05-27 04:32:45.4783 [INFO] [StorageConcurrencyTest]::TestBasicReadWriteMutex(79) - --- 测试1: 基本读写互斥 ---
2025-05-27 04:32:45.4783 [INFO] [StorageInstance]::Set(102) - Instance [ConcurrencyTest] - Key: testKey, Type: String, Value: initialValue
2025-05-27 04:32:45.4783 [INFO] [StorageInstance]::Set(102) - Instance [ConcurrencyTest] - Key: counter, Type: Int32, Value: 0
2025-05-27 04:32:45.4783 [INFO] [StorageInstance]::SaveToFileAsync(274) - Instance [ConcurrencyTest] - Starting async save operation
2025-05-27 04:32:45.4783 [INFO] [StorageInstance]::SaveToFileInBackgroundWithWriteLock(827) - Acquiring write lock for file operation: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\CharacterConfig.json
2025-05-27 04:32:45.4783 [INFO] [StorageInstance]::SaveToFileInBackgroundWithWriteLock(833) - Write lock acquired, starting file save: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\CharacterConfig.json
2025-05-27 04:32:45.5227 [INFO] [Storage]::WriteToFileWithBackup(242) - File saved successfully: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\CharacterConfig.json
2025-05-27 04:32:45.5227 [INFO] [StorageInstance]::SaveToFileInBackgroundWithWriteLock(839) - Write lock released for file: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\CharacterConfig.json
2025-05-27 04:32:46.1515 [INFO] [StorageInstance]::SaveToFileInBackgroundInternal(700) - Saved 2 items to file: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\CharacterConfig.json
2025-05-27 04:32:46.1515 [INFO] [StorageInstance]::SaveToFileAsync(285) - Instance [ConcurrencyTest] - Data saved successfully
2025-05-27 04:32:46.1515 [INFO] [StorageConcurrencyTest]::LogOperation(235) - [001] 写操作完成: 成功 - 
2025-05-27 04:32:46.2387 [INFO] [StorageInstance]::LoadFromFileAsync(317) - Instance [ConcurrencyTest] - Starting async load operation
2025-05-27 04:32:46.2387 [INFO] [StorageInstance]::LoadFromFileInBackgroundWithReadLock(872) - Acquiring read lock for file operation: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\CharacterConfig.json
2025-05-27 04:32:46.2387 [INFO] [StorageInstance]::LoadFromFileInBackgroundWithReadLock(878) - Read lock acquired, starting file load: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\CharacterConfig.json
2025-05-27 04:32:46.2413 [INFO] [StorageInstance]::LoadFromFileInBackgroundWithReadLock(891) - Starting decryption and deserialization in background thread: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\CharacterConfig.json
2025-05-27 04:32:46.2413 [INFO] [StorageInstance]::LoadFromFileInBackgroundWithReadLock(895) - Background deserialization completed, 2 items processed: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\CharacterConfig.json
2025-05-27 04:32:46.2413 [INFO] [StorageInstance]::LoadFromFileInBackgroundWithReadLock(901) - Read lock released for file: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\CharacterConfig.json
2025-05-27 04:32:46.2822 [INFO] [StorageInstance]::LoadFromFileInBackgroundWithReadLock(916) - Loaded 2 items from file: C:/Users/<USER>/AppData/LocalLow/DefaultCompany/DGame\CharacterConfig.json
2025-05-27 04:32:46.2822 [INFO] [StorageCache]::LoadFromDictionary(218) - Loaded 2 items into cache
2025-05-27 04:32:46.2822 [INFO] [StorageInstance]::LoadFromFileAsync(328) - Instance [ConcurrencyTest] - Data loaded successfully, 2 items
2025-05-27 04:32:46.2822 [INFO] [StorageConcurrencyTest]::LogOperation(235) - [002] 读操作完成: 成功 - 
