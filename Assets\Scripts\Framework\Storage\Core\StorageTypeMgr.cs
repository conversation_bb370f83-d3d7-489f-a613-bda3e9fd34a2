using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using DGame.Framework;
using UnityEngine;

namespace Storage
{
    /// <summary>
    /// 存储类型管理器
    /// 负责创建和管理类型包装器，类似于ES3的ES3TypeMgr
    /// </summary>
    public static class StorageTypeMgr
    {
        #region 私有字段

        private static readonly object _lock = new object();
        private static Dictionary<Type, Func<StorageTypeWrapper>> _typeFactories;
        private static bool _isInitialized = false;

        // 缓存最后访问的类型工厂，提高性能
        private static Type _lastAccessedType = null;
        private static Func<StorageTypeWrapper> _lastAccessedFactory = null;

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化类型管理器
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized)
                return;

            lock (_lock)
            {
                if (_isInitialized)
                    return;

                _typeFactories = new Dictionary<Type, Func<StorageTypeWrapper>>();

                // 注册基础类型
                RegisterPrimitiveTypes();

                // 注册Unity类型
                RegisterUnityTypes();

                // 注册集合类型
                RegisterCollectionTypes();

                _isInitialized = true;
                NLogger.Log("StorageTypeMgr initialized with {0} registered types", arg0: _typeFactories.Count);
            }
        }

        /// <summary>
        /// 注册基础类型（使用特化包装器提高性能）
        /// </summary>
        private static void RegisterPrimitiveTypes()
        {
            // 使用特化包装器的高性能类型
            _typeFactories[typeof(int)] = () => new StorageTypeWrapper_int();
            _typeFactories[typeof(float)] = () => new StorageTypeWrapper_float();
            _typeFactories[typeof(bool)] = () => new StorageTypeWrapper_bool();
            _typeFactories[typeof(long)] = () => new StorageTypeWrapper_long();
            _typeFactories[typeof(double)] = () => new StorageTypeWrapper_double();
            _typeFactories[typeof(string)] = () => new StorageTypeWrapper_string();

            // 使用泛型包装器的其他类型
            RegisterType<byte>();
            RegisterType<sbyte>();
            RegisterType<short>();
            RegisterType<ushort>();
            RegisterType<uint>();
            RegisterType<ulong>();
            RegisterType<decimal>();
            RegisterType<char>();
            RegisterType<DateTime>();
            RegisterType<Guid>();
        }

        /// <summary>
        /// 注册Unity类型（使用特化包装器提高性能）
        /// </summary>
        private static void RegisterUnityTypes()
        {
            // 使用特化包装器的高性能Unity类型
            _typeFactories[typeof(Vector2)] = () => new StorageTypeWrapper_Vector2();
            _typeFactories[typeof(Vector3)] = () => new StorageTypeWrapper_Vector3();
            _typeFactories[typeof(Quaternion)] = () => new StorageTypeWrapper_Quaternion();
            _typeFactories[typeof(Color)] = () => new StorageTypeWrapper_Color();
            _typeFactories[typeof(Rect)] = () => new StorageTypeWrapper_Rect();
            _typeFactories[typeof(Bounds)] = () => new StorageTypeWrapper_Bounds();

            // 使用泛型包装器的其他Unity类型
            RegisterType<Vector4>();
            RegisterType<Vector2Int>();
            RegisterType<Vector3Int>();
            RegisterType<Color32>();
            RegisterType<Matrix4x4>();
            RegisterType<LayerMask>();
        }

        /// <summary>
        /// 注册集合类型（基础支持）
        /// </summary>
        private static void RegisterCollectionTypes()
        {
            // 数组类型
            RegisterType<int[]>();
            RegisterType<float[]>();
            RegisterType<string[]>();
            RegisterType<bool[]>();

            // 常用List类型
            RegisterType<List<int>>();
            RegisterType<List<float>>();
            RegisterType<List<string>>();
            RegisterType<List<bool>>();
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 注册类型
        /// </summary>
        /// <typeparam name="T">要注册的类型</typeparam>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static void RegisterType<T>()
        {
            if (!_isInitialized)
                Initialize();

            var type = typeof(T);
            lock (_lock)
            {
                _typeFactories[type] = () => new StorageTypeWrapper<T>();
            }
        }

        /// <summary>
        /// 创建指定类型的包装器
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <returns>类型包装器</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static StorageTypeWrapper<T> CreateWrapper<T>()
        {
            return new StorageTypeWrapper<T>();
        }

        /// <summary>
        /// 创建指定类型的包装器并设置值
        /// </summary>
        /// <typeparam name="T">类型</typeparam>
        /// <param name="value">初始值</param>
        /// <returns>类型包装器</returns>
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static StorageTypeWrapper<T> CreateWrapper<T>(T value)
        {
            return new StorageTypeWrapper<T>(value);
        }

        /// <summary>
        /// 根据类型创建包装器
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>类型包装器</returns>
        public static StorageTypeWrapper CreateWrapper(Type type)
        {
            if (!_isInitialized)
                Initialize();

            // 使用缓存提高性能
            if (type == _lastAccessedType && _lastAccessedFactory != null)
            {
                return _lastAccessedFactory();
            }

            if (_typeFactories.TryGetValue(type, out var factory))
            {
                _lastAccessedType = type;
                _lastAccessedFactory = factory;
                return factory();
            }

            // 如果没有注册的类型，尝试动态创建
            return CreateDynamicWrapper(type);
        }

        /// <summary>
        /// 根据值创建包装器
        /// </summary>
        /// <param name="value">值</param>
        /// <returns>类型包装器</returns>
        public static StorageTypeWrapper CreateWrapperFromValue(object value)
        {
            if (value == null)
            {
                return CreateWrapper<object>();
            }

            var wrapper = CreateWrapper(value.GetType());
            wrapper.SetValue(value);
            return wrapper;
        }

        /// <summary>
        /// 检查类型是否已注册
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>是否已注册</returns>
        public static bool IsTypeRegistered(Type type)
        {
            if (!_isInitialized)
                Initialize();

            return _typeFactories.ContainsKey(type);
        }

        /// <summary>
        /// 获取所有已注册的类型
        /// </summary>
        /// <returns>已注册的类型集合</returns>
        public static ICollection<Type> GetRegisteredTypes()
        {
            if (!_isInitialized)
                Initialize();

            return _typeFactories.Keys;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 动态创建包装器（用于未注册的类型）
        /// </summary>
        /// <param name="type">类型</param>
        /// <returns>类型包装器</returns>
        private static StorageTypeWrapper CreateDynamicWrapper(Type type)
        {
            try
            {
                // 使用反射创建泛型包装器
                var wrapperType = typeof(StorageTypeWrapper<>).MakeGenericType(type);
                var wrapper = (StorageTypeWrapper)Activator.CreateInstance(wrapperType);

                // 动态注册这个类型以提高后续性能
                lock (_lock)
                {
                    if (!_typeFactories.ContainsKey(type))
                    {
                        _typeFactories[type] = () => (StorageTypeWrapper)Activator.CreateInstance(wrapperType);
                        NLogger.Log("Dynamically registered type: {0}", arg0: type.Name);
                    }
                }

                return wrapper;
            }
            catch (Exception ex)
            {
                NLogger.LogError("Failed to create dynamic wrapper for type {0}: {1}", arg0: type.Name, arg1: ex.Message);

                // 回退到object类型包装器
                return new StorageTypeWrapper<object>();
            }
        }

        #endregion
    }
}
