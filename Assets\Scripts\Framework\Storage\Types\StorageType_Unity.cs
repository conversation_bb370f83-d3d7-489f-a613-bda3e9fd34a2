using System;
using System.Runtime.CompilerServices;
using UnityEngine;

namespace Storage
{
    /// <summary>
    /// Unity类型的特化包装器实现
    /// 提供高性能的Unity值类型存储，避免装箱拆箱操作
    /// </summary>
    
    #region Vector类型特化包装器

    /// <summary>
    /// Vector3类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_Vector3 : StorageTypeWrapper
    {
        private Vector3 _value;

        public StorageTypeWrapper_Vector3() { }
        public StorageTypeWrapper_Vector3(Vector3 value) { _value = value; }

        public override Type GetValueType() => typeof(Vector3);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(Vector3))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(Vector3))
            {
                _value = (Vector3)(object)value;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out Vector3 converted))
                _value = converted;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_Vector3();
        public override string GetValueString() => $"({_value.x:F3}, {_value.y:F3}, {_value.z:F3})";
        
        public Vector3 GetValue() => _value;
        public void SetValue(Vector3 value) => _value = value;
    }

    /// <summary>
    /// Vector2类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_Vector2 : StorageTypeWrapper
    {
        private Vector2 _value;

        public StorageTypeWrapper_Vector2() { }
        public StorageTypeWrapper_Vector2(Vector2 value) { _value = value; }

        public override Type GetValueType() => typeof(Vector2);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(Vector2))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(Vector2))
            {
                _value = (Vector2)(object)value;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out Vector2 converted))
                _value = converted;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_Vector2();
        public override string GetValueString() => $"({_value.x:F3}, {_value.y:F3})";
        
        public Vector2 GetValue() => _value;
        public void SetValue(Vector2 value) => _value = value;
    }

    /// <summary>
    /// Quaternion类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_Quaternion : StorageTypeWrapper
    {
        private Quaternion _value;

        public StorageTypeWrapper_Quaternion() { _value = Quaternion.identity; }
        public StorageTypeWrapper_Quaternion(Quaternion value) { _value = value; }

        public override Type GetValueType() => typeof(Quaternion);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(Quaternion))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(Quaternion))
            {
                _value = (Quaternion)(object)value;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out Quaternion converted))
                _value = converted;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_Quaternion();
        public override string GetValueString() => $"({_value.x:F5}, {_value.y:F5}, {_value.z:F5}, {_value.w:F5})";
        
        public Quaternion GetValue() => _value;
        public void SetValue(Quaternion value) => _value = value;
    }

    /// <summary>
    /// Color类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_Color : StorageTypeWrapper
    {
        private Color _value;

        public StorageTypeWrapper_Color() { _value = Color.white; }
        public StorageTypeWrapper_Color(Color value) { _value = value; }

        public override Type GetValueType() => typeof(Color);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(Color))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(Color))
            {
                _value = (Color)(object)value;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out Color converted))
                _value = converted;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_Color();
        public override string GetValueString() => $"RGBA({_value.r:F3}, {_value.g:F3}, {_value.b:F3}, {_value.a:F3})";
        
        public Color GetValue() => _value;
        public void SetValue(Color value) => _value = value;
    }

    #endregion

    #region 其他Unity类型特化包装器

    /// <summary>
    /// Rect类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_Rect : StorageTypeWrapper
    {
        private Rect _value;

        public StorageTypeWrapper_Rect() { }
        public StorageTypeWrapper_Rect(Rect value) { _value = value; }

        public override Type GetValueType() => typeof(Rect);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(Rect))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(Rect))
            {
                _value = (Rect)(object)value;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out Rect converted))
                _value = converted;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_Rect();
        public override string GetValueString() => $"Rect({_value.x:F2}, {_value.y:F2}, {_value.width:F2}, {_value.height:F2})";
        
        public Rect GetValue() => _value;
        public void SetValue(Rect value) => _value = value;
    }

    /// <summary>
    /// Bounds类型特化包装器
    /// </summary>
    public sealed class StorageTypeWrapper_Bounds : StorageTypeWrapper
    {
        private Bounds _value;

        public StorageTypeWrapper_Bounds() { }
        public StorageTypeWrapper_Bounds(Bounds value) { _value = value; }

        public override Type GetValueType() => typeof(Bounds);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool TryGetValue<T>(out T value)
        {
            if (typeof(T) == typeof(Bounds))
            {
                value = (T)(object)_value;
                return true;
            }
            return StorageTypeConverter.TryConvert(_value, out value);
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override void SetValue<T>(T value)
        {
            if (typeof(T) == typeof(Bounds))
            {
                _value = (Bounds)(object)value;
                return;
            }
            if (StorageTypeConverter.TryConvert(value, out Bounds converted))
                _value = converted;
        }

        public override object GetBoxedValue() => _value;
        public override StorageTypeWrapper CreateInstance() => new StorageTypeWrapper_Bounds();
        public override string GetValueString() => $"Bounds(center: {_value.center}, size: {_value.size})";
        
        public Bounds GetValue() => _value;
        public void SetValue(Bounds value) => _value = value;
    }

    #endregion
}
