{"version": 3, "targets": {".NETStandard,Version=v2.1": {"SingularityGroup.HotReload.Runtime.Public/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/SingularityGroup.HotReload.Runtime.Public.dll": {}}, "runtime": {"bin/placeholder/SingularityGroup.HotReload.Runtime.Public.dll": {}}}, "Tayx.Graphy/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Tayx.Graphy.dll": {}}, "runtime": {"bin/placeholder/Tayx.Graphy.dll": {}}}, "Tayx.Graphy.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Tayx.Graphy": "1.0.0"}, "compile": {"bin/placeholder/Tayx.Graphy.Editor.dll": {}}, "runtime": {"bin/placeholder/Tayx.Graphy.Editor.dll": {}}}}}, "libraries": {"SingularityGroup.HotReload.Runtime.Public/1.0.0": {"type": "project", "path": "SingularityGroup.HotReload.Runtime.Public.csproj", "msbuildProject": "SingularityGroup.HotReload.Runtime.Public.csproj"}, "Tayx.Graphy/1.0.0": {"type": "project", "path": "Tayx.Graphy.csproj", "msbuildProject": "Tayx.Graphy.csproj"}, "Tayx.Graphy.Editor/1.0.0": {"type": "project", "path": "Tayx.Graphy.Editor.csproj", "msbuildProject": "Tayx.Graphy.Editor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["SingularityGroup.HotReload.Runtime.Public >= 1.0.0", "Tayx.Graphy >= 1.0.0", "Tayx.Graphy.Editor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "H:\\DiceGame\\DGame\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "H:\\DiceGame\\DGame\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "H:\\DiceGame\\DGame\\Temp\\obj\\Debug\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"H:\\DiceGame\\DGame\\SingularityGroup.HotReload.Runtime.Public.csproj": {"projectPath": "H:\\DiceGame\\DGame\\SingularityGroup.HotReload.Runtime.Public.csproj"}, "H:\\DiceGame\\DGame\\Tayx.Graphy.csproj": {"projectPath": "H:\\DiceGame\\DGame\\Tayx.Graphy.csproj"}, "H:\\DiceGame\\DGame\\Tayx.Graphy.Editor.csproj": {"projectPath": "H:\\DiceGame\\DGame\\Tayx.Graphy.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}